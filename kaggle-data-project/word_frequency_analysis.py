#!/usr/bin/env python3
"""
Word Frequency Analysis Script

This script analyzes the partial_extraction.csv file to find the 50 most frequent words
(4+ characters) in the preceding_words column for each type (Missing, Primary, Secondary).
It excludes cases where dataset extraction could not find the dataset.
"""

import pandas as pd
import re
from collections import Counter
import os

def clean_and_tokenize_text(text):
    """
    Clean text and extract words with 4+ characters.
    
    Args:
        text (str): Input text to process
        
    Returns:
        list: List of cleaned words with 4+ characters
    """
    if pd.isna(text) or text == '':
        return []
    
    # Convert to lowercase
    text = text.lower()
    
    # Remove URLs, DOIs, and other patterns
    text = re.sub(r'https?://[^\s]+', '', text)
    text = re.sub(r'doi[:\s]*[^\s]+', '', text)
    text = re.sub(r'www\.[^\s]+', '', text)
    
    # Remove special characters and numbers, keep only letters and spaces
    text = re.sub(r'[^a-z\s]', ' ', text)
    
    # Split into words and filter for 4+ characters
    words = [word.strip() for word in text.split() if len(word.strip()) >= 4]
    
    return words

def analyze_word_frequencies(csv_file_path):
    """
    Analyze word frequencies in the partial_extraction.csv file.
    
    Args:
        csv_file_path (str): Path to the CSV file
    """
    # Read the CSV file
    try:
        df = pd.read_csv(csv_file_path)
        print(f"Loaded {len(df)} records from {csv_file_path}")
    except FileNotFoundError:
        print(f"Error: File {csv_file_path} not found.")
        return
    except Exception as e:
        print(f"Error reading CSV file: {e}")
        return
    
    # Check required columns
    required_columns = ['article_id', 'dataset_id', 'preceding_words', 'type']
    missing_columns = [col for col in required_columns if col not in df.columns]
    if missing_columns:
        print(f"Error: Missing required columns: {missing_columns}")
        return
    
    # Filter out cases where dataset extraction failed
    # We exclude rows where dataset_id is 'Missing' or contains 'DATASET_ID_NOT_FOUND'
    original_count = len(df)
    df_filtered = df[
        (df['dataset_id'] != 'Missing') & 
        (~df['dataset_id'].str.contains('DATASET_ID_NOT_FOUND', na=False))
    ].copy()
    
    print(f"Filtered out {original_count - len(df_filtered)} records where dataset extraction failed")
    print(f"Analyzing {len(df_filtered)} records with successful dataset extraction")
    
    # Get unique types
    types = df_filtered['type'].unique()
    print(f"Found types: {list(types)}")
    
    # Analyze word frequencies for each type
    results = {}
    
    for data_type in types:
        print(f"\nAnalyzing type: {data_type}")
        
        # Filter data for current type
        type_data = df_filtered[df_filtered['type'] == data_type]
        print(f"  Records for {data_type}: {len(type_data)}")
        
        # Collect all words for this type
        all_words = []
        for _, row in type_data.iterrows():
            words = clean_and_tokenize_text(row['preceding_words'])
            all_words.extend(words)
        
        print(f"  Total words extracted: {len(all_words)}")
        
        # Count word frequencies
        word_counts = Counter(all_words)
        
        # Get top 50 most frequent words
        top_50_words = word_counts.most_common(50)
        
        results[data_type] = {
            'record_count': len(type_data),
            'total_words': len(all_words),
            'unique_words': len(word_counts),
            'top_50': top_50_words
        }
        
        print(f"  Unique words: {len(word_counts)}")
        print(f"  Top 10 words: {top_50_words[:10]}")
    
    # Save results to file
    output_file = 'results/word_frequency_analysis.txt'
    os.makedirs('results', exist_ok=True)
    
    with open(output_file, 'w', encoding='utf-8') as f:
        f.write("Word Frequency Analysis Results\n")
        f.write("=" * 50 + "\n\n")
        f.write(f"Analysis of: {csv_file_path}\n")
        f.write(f"Total records analyzed: {len(df_filtered)}\n")
        f.write(f"Records excluded (failed extraction): {original_count - len(df_filtered)}\n\n")
        
        for data_type in sorted(results.keys()):
            f.write(f"\nTYPE: {data_type.upper()}\n")
            f.write("-" * 30 + "\n")
            f.write(f"Records: {results[data_type]['record_count']}\n")
            f.write(f"Total words: {results[data_type]['total_words']}\n")
            f.write(f"Unique words: {results[data_type]['unique_words']}\n\n")
            f.write("Top 50 Most Frequent Words (4+ characters):\n")
            f.write("Rank | Word | Frequency\n")
            f.write("-----|------|----------\n")
            
            for rank, (word, count) in enumerate(results[data_type]['top_50'], 1):
                f.write(f"{rank:4d} | {word:20s} | {count:6d}\n")
            f.write("\n")
    
    print(f"\nResults saved to: {output_file}")
    
    # Also create a CSV summary
    csv_output_file = 'results/word_frequency_summary.csv'
    summary_data = []
    
    for data_type in sorted(results.keys()):
        for rank, (word, count) in enumerate(results[data_type]['top_50'], 1):
            summary_data.append({
                'type': data_type,
                'rank': rank,
                'word': word,
                'frequency': count
            })
    
    summary_df = pd.DataFrame(summary_data)
    summary_df.to_csv(csv_output_file, index=False)
    print(f"CSV summary saved to: {csv_output_file}")
    
    # Print summary statistics
    print(f"\nSUMMARY STATISTICS:")
    print("=" * 50)
    for data_type in sorted(results.keys()):
        print(f"{data_type:10s}: {results[data_type]['record_count']:4d} records, "
              f"{results[data_type]['total_words']:6d} words, "
              f"{results[data_type]['unique_words']:5d} unique")

def main():
    """Main function to run the analysis."""
    csv_file_path = 'results/partial_extraction.csv'
    
    if not os.path.exists(csv_file_path):
        print(f"Error: File {csv_file_path} not found.")
        print("Please make sure the partial_extraction.csv file exists in the results directory.")
        return
    
    analyze_word_frequencies(csv_file_path)

if __name__ == "__main__":
    main()
