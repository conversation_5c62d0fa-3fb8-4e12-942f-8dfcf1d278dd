"""
Script final para extrair dataset_ids de TODOS os PDFs.
"""

import pandas as pd
import re
import random
from pathlib import Path
from pypdf import PdfReader
from tqdm import tqdm


def clean_text_for_urls(text):
    """Clean text to handle URLs split across lines and other formatting issues."""
    # Fix hyphenated words split across lines (remove hyphen and join words)
    text = re.sub(r'(\w+)-\s*\n\s*(\w+)', r'\1\2', text)

    # Fix common URL splits
    text = re.sub(r'https://doi\.\s*\n\s*org/', 'https://doi.org/', text)
    text = re.sub(r'doi\.org/\s*\n\s*', 'doi.org/', text)
    text = re.sub(r'https://doi\.\s+org/', 'https://doi.org/', text)
    text = re.sub(r'10\.\d+/\s*\n\s*', '10.', text)

    # Fix dryad URLs specifically
    text = re.sub(r'dryad\.\s*\n\s*', 'dryad.', text)
    text = re.sub(r'10\.5061/\s*\n\s*dryad\.', '10.5061/dryad.', text)

    # Fix other common splits in dataset IDs
    text = re.sub(r'(\w+)\s*\n\s*(\w+)', r'\1\2', text)
    text = re.sub(r'(\d+)\s*\n\s*(\d+)', r'\1\2', text)

    # Remove excessive whitespace but preserve single spaces
    text = re.sub(r'\s+', ' ', text)

    return text


def convert_incomplete_dataset_id_to_doi(dataset_id):
    """
    Convert incomplete dataset IDs to full DOI or database URLs.

    Args:
        dataset_id (str): The dataset ID to convert

    Returns:
        str: Full DOI URL or database URL
    """
    if str(dataset_id) in ['nan', 'None', ''] or dataset_id == "Missing" or dataset_id == "DATASET_ID_NOT_FOUND":
        return dataset_id

    dataset_id = str(dataset_id).strip()

    # Skip if already a full URL
    if dataset_id.startswith('http'):
        return dataset_id

    # ChEMBL IDs
    if dataset_id.startswith('CHEMBL'):
        return f"https://doi.org.br/10.6019/chembl.{dataset_id.lower()}"

    # InterPro IDs
    elif dataset_id.startswith('IPR'):
        return f"https://doi.org.br/10.6019/interpro.{dataset_id.lower()}"

    # Pfam IDs
    elif dataset_id.startswith('PF'):
        return f"https://doi.org.br/10.6019/pfam.{dataset_id.lower()}"

    # Gene Expression Omnibus (GEO) IDs
    elif dataset_id.startswith('GSE'):
        return f"https://doi.org.br/10.6019/geo.{dataset_id.lower()}"

    # Sequence Read Archive (SRA) IDs
    elif dataset_id.startswith('SRP') or dataset_id.startswith('SRR') or dataset_id.startswith('ERR'):
        return f"https://doi.org.br/10.6019/sra.{dataset_id.lower()}"

    # EMPIAR IDs
    elif dataset_id.startswith('EMPIAR-'):
        empiar_id = dataset_id.replace('EMPIAR-', '')
        return f"https://doi.org.br/10.6019/empiar.{empiar_id}"

    # Ensembl IDs
    elif dataset_id.startswith('ENSBTAG'):
        return f"https://doi.org.br/10.6019/ensembl.{dataset_id.lower()}"

    # NCBI BioSample IDs
    elif dataset_id.startswith('SAMN'):
        return f"https://doi.org.br/10.6019/biosample.{dataset_id.lower()}"

    # GISAID EPI IDs
    elif dataset_id.startswith('EPI'):
        return f"https://doi.org.br/10.6019/gisaid.{dataset_id.lower()}"

    # GenBank accession numbers (various patterns)
    elif re.match(r'^[A-Z]{1,2}\d{5,6}$', dataset_id):
        return f"https://doi.org.br/10.6019/genbank.{dataset_id.lower()}"

    # NCBI Protein IDs
    elif re.match(r'^[A-Z]{3}\d{5}$', dataset_id):
        return f"https://doi.org.br/10.6019/protein.{dataset_id.lower()}"

    # PDB IDs (4 characters)
    elif re.match(r'^[0-9][A-Z0-9]{3}$', dataset_id):
        return f"https://doi.org.br/10.6019/pdb.{dataset_id.lower()}"

    # Cell Line IDs (CVCL)
    elif dataset_id.startswith('CVCL_'):
        return f"https://doi.org.br/10.6019/cellosaurus.{dataset_id.lower()}"

    # BioModels IDs
    elif dataset_id.startswith('MODEL'):
        return f"https://doi.org.br/10.6019/biomodels.{dataset_id.lower()}"

    # NCBI Project IDs
    elif dataset_id.startswith('PRJNA'):
        return f"https://doi.org.br/10.6019/bioproject.{dataset_id.lower()}"

    # Other specific patterns that might be database IDs
    else:
        # Keep as is for manual review
        return dataset_id


def normalize_dataset_id_to_standard_format(dataset_id):
    """Normaliza o dataset_id para o formato padrão https://doi.org.br/doi_number."""
    if dataset_id == "Missing":
        return "Missing"

    # First try to convert incomplete IDs to full DOI format
    converted_id = convert_incomplete_dataset_id_to_doi(dataset_id)
    if converted_id != dataset_id:
        return converted_id

    # Extrair o DOI number do dataset_id
    doi_number = None

    # Caso 1: Já está no formato https://doi.org/10.xxxx/xxxx
    if "doi.org/" in dataset_id:
        doi_number = dataset_id.split("doi.org/", 1)[1]

    # Caso 2: Começa com 10.xxxx/xxxx (DOI direto)
    elif dataset_id.startswith("10."):
        doi_number = dataset_id

    # Caso 3: Contém dryad (formato especial)
    elif "dryad" in dataset_id.lower():
        # Manter formato dryad mas normalizar URL
        if "10.5061/dryad" in dataset_id:
            doi_match = re.search(r'10\.5061/dryad\.[\w\d\.-]+', dataset_id)
            if doi_match:
                doi_number = doi_match.group()
        else:
            # Se não tem o formato completo, tentar extrair
            dryad_match = re.search(r'dryad\.([\w\d\.-]+)', dataset_id, re.IGNORECASE)
            if dryad_match:
                doi_number = f"10.5061/dryad.{dryad_match.group(1)}"

    # Caso 4: Outros formatos com DOI
    else:
        # Tentar extrair qualquer padrão de DOI
        doi_match = re.search(r'10\.\d+/[\w\d\.-]+', dataset_id)
        if doi_match:
            doi_number = doi_match.group()

    # Se conseguiu extrair o DOI number, formatar no padrão
    if doi_number:
        # Limpar o DOI number
        doi_number = doi_number.strip()
        return f"https://doi.org.br/{doi_number}"

    # Se não conseguiu extrair, retornar o original
    return dataset_id


def normalize_dataset_id(dataset_id):
    """Normaliza o dataset_id para diferentes variações para busca."""
    if dataset_id == "Missing":
        return []

    variations = [dataset_id]  # Versão completa

    # Se começar com https://, adicionar versão sem https://
    if dataset_id.startswith("https://"):
        without_https = dataset_id.replace("https://", "")
        variations.append(without_https)

        # Se contém doi.org, adicionar apenas a parte após doi.org/
        if "doi.org/" in without_https:
            after_doi = without_https.split("doi.org/", 1)[1]
            variations.append(after_doi)

        # Adicionar versão com doi: prefix
        variations.append(f"doi:{dataset_id}")
        variations.append(f"doi: {dataset_id}")
        variations.append(f"doi:https://{without_https}")

    # Se não começar com https:// mas contém doi.org, adicionar a parte após doi.org/
    elif "doi.org/" in dataset_id:
        after_doi = dataset_id.split("doi.org/", 1)[1]
        variations.append(after_doi)
        variations.append(f"https://{dataset_id}")
        variations.append(f"doi:{dataset_id}")

    # Adicionar variações específicas para DOIs
    if "10." in dataset_id:
        # Extrair apenas a parte do DOI
        doi_match = re.search(r'10\.\d+/[\w\d\.-]+', dataset_id)
        if doi_match:
            doi_part = doi_match.group()
            variations.append(doi_part)
            variations.append(f"doi:{doi_part}")
            variations.append(f"doi: {doi_part}")

    # Adicionar variações para dryad específicamente
    if "dryad" in dataset_id.lower():
        # Extrair apenas o identificador após dryad.
        dryad_match = re.search(r'dryad\.(\w+)', dataset_id, re.IGNORECASE)
        if dryad_match:
            dryad_id = dryad_match.group(1)
            variations.append(dryad_id)
            variations.append(f"dryad.{dryad_id}")

    # Remover duplicatas mantendo ordem
    seen = set()
    unique_variations = []
    for var in variations:
        if var not in seen:
            seen.add(var)
            unique_variations.append(var)

    return unique_variations


def filter_meaningful_words(words, target_parts=None):
    """Filtra palavras para manter apenas texto contextual significativo."""
    if not words:
        return []

    # Palavras a evitar (números isolados, URLs, etc.)
    avoid_patterns = [
        r'^\d+$',  # Números isolados
        r'^[IVX]+$',  # Números romanos
        r'^(https?|doi|www|org|com|net)$',  # Partes de URLs
        r'^(fig|figure|table|ref|references?)$',  # Referências de figuras/tabelas
        r'^[a-z]$',  # Letras isoladas
        r'^\d{4}$',  # Anos isolados
        r'^(pp?|vol|no|issue)$',  # Termos de publicação
    ]

    # Se temos partes do target, evitá-las também
    if target_parts:
        for part in target_parts:
            if len(part) > 2:  # Só partes significativas
                avoid_patterns.append(rf'^{re.escape(part)}$')

    filtered_words = []
    for word in words:
        word_lower = word.lower()
        # Verificar se a palavra deve ser evitada
        should_avoid = False
        for pattern in avoid_patterns:
            if re.match(pattern, word_lower, re.IGNORECASE):
                should_avoid = True
                break

        if not should_avoid:
            filtered_words.append(word)

    return filtered_words


def extract_preceding_words(text, target, num_words=10):
    """Extrai as palavras que antecedem um target no texto com busca robusta."""
    # Clean text first
    cleaned_text = clean_text_for_urls(text)

    # Extrair partes do target para evitar no resultado
    target_parts = []
    if "doi.org/" in target:
        target_parts.extend(target.split('/'))
    elif target.startswith("10."):
        target_parts.extend(re.split(r'[/\.]', target))
    elif "dryad" in target.lower():
        target_parts.extend(re.split(r'[/\.]', target))

    # Try exact match first (mais flexível com quebras de linha)
    try:
        # Normalizar espaços e quebras de linha no target para busca
        target_normalized = re.sub(r'\s+', r'\\s*', re.escape(target))
        pattern = rf'(.+?){target_normalized}'
        match = re.search(pattern, cleaned_text, re.IGNORECASE | re.DOTALL)

        if match:
            preceding_text = match.group(1)
            # Pegar as últimas 200 palavras para ter contexto suficiente
            words = re.findall(r'\b[a-zA-Z][a-zA-Z]+\b', preceding_text)
            # Filtrar palavras significativas
            filtered_words = filter_meaningful_words(words, target_parts)

            if len(filtered_words) >= num_words:
                return ' '.join(filtered_words[-num_words:])
            elif len(filtered_words) >= 3:  # Pelo menos 3 palavras significativas
                return ' '.join(filtered_words)
    except Exception:
        pass

    # Try partial matching for DOIs and URLs (mais agressivo)
    if "doi.org/" in target or target.startswith("10.") or "dryad" in target.lower():
        try:
            # Extract the key part for searching
            if "doi.org/" in target:
                key_part = target.split("doi.org/", 1)[1]
            elif target.startswith("10."):
                key_part = target
            elif "dryad" in target.lower():
                # Extract dryad identifier
                dryad_match = re.search(r'dryad\.(\w+)', target, re.IGNORECASE)
                if dryad_match:
                    key_part = dryad_match.group(1)
                else:
                    key_part = target
            else:
                key_part = target

            # Clean key part e tentar diferentes variações
            key_clean = re.sub(r'[^\w/.-]', '', key_part)

            if key_clean and len(key_clean) > 3:  # Só buscar partes significativas
                # Tentar buscar com diferentes padrões
                escaped_key = re.escape(key_clean)
                escaped_key_dots = re.escape(key_clean.replace(".", "."))
                search_patterns = [
                    rf'(.+?){escaped_key}',  # Busca direta
                    rf'(.+?){escaped_key_dots}',  # Escapar pontos
                    rf'(.+?){escaped_key}\b',  # Com word boundary
                ]

                for pattern in search_patterns:
                    try:
                        match = re.search(pattern, cleaned_text, re.IGNORECASE | re.DOTALL)
                        if match:
                            preceding_text = match.group(1)
                            # Pegar as últimas palavras
                            words = re.findall(r'\b[a-zA-Z][a-zA-Z]+\b', preceding_text)
                            # Filtrar palavras significativas
                            filtered_words = filter_meaningful_words(words, target_parts)

                            if len(filtered_words) >= num_words:
                                return ' '.join(filtered_words[-num_words:])
                            elif len(filtered_words) >= 3:  # Pelo menos 3 palavras significativas
                                return ' '.join(filtered_words)
                    except Exception:
                        continue
        except Exception:
            pass

    # Try searching for just the DOI number part (mais agressivo ainda)
    try:
        # Extrair apenas o número DOI (ex: 10.1002/2017jc013030)
        doi_match = re.search(r'10\.\d+/[\w\d\.-]+', target)
        if doi_match:
            doi_number = doi_match.group()
            # Buscar por este número no texto
            escaped_doi = re.escape(doi_number).replace(r'\/', r'[/\s]*').replace(r'\.', r'[.\s]*')
            pattern = rf'(.+?){escaped_doi}'
            match = re.search(pattern, cleaned_text, re.IGNORECASE | re.DOTALL)

            if match:
                preceding_text = match.group(1)
                words = re.findall(r'\b[a-zA-Z][a-zA-Z]+\b', preceding_text)
                filtered_words = filter_meaningful_words(words, target_parts)

                if len(filtered_words) >= num_words:
                    return ' '.join(filtered_words[-num_words:])
                elif len(filtered_words) >= 3:
                    return ' '.join(filtered_words)
    except Exception:
        pass

    return None


def search_in_sections(text, target, sections=None):
    """Busca o target em seções específicas do texto."""
    if sections is None:
        sections = [
            "DATA AVAILABILITY STATEMENT",
            "DATA AVAILABILITY",
            "DATA ACCESSIBILITY",
            "ACKNOWLEDGMENT",
            "ACKNOWLEDGMENTS",
            "SUPPORTING INFORMATION",
            "SUPPLEMENTARY MATERIAL"
        ]

    cleaned_text = clean_text_for_urls(text)

    for section in sections:
        # Find section in text
        section_pattern = rf'{re.escape(section)}(.{{0,2000}}?)(?:REFERENCES|CONFLICT|AUTHOR|FUNDING|\n\n[A-Z]{{3,}}|\Z)'
        section_match = re.search(section_pattern, cleaned_text, re.IGNORECASE | re.DOTALL)

        if section_match:
            section_text = section_match.group(1)
            # Try to find target in this section
            preceding_words = extract_preceding_words(section_text, target)
            if preceding_words:
                return preceding_words

    return None


def extract_random_words(text, num_words=10):
    """Extrai num_words palavras consecutivas aleatórias significativas do texto."""
    # Extrair apenas palavras alfabéticas (não números isolados)
    words = re.findall(r'\b[a-zA-Z][a-zA-Z]+\b', text)

    # Filtrar palavras significativas
    filtered_words = filter_meaningful_words(words)

    if len(filtered_words) < num_words:
        if len(filtered_words) >= 3:  # Pelo menos 3 palavras significativas
            return ' '.join(filtered_words)
        else:
            return "insufficient_meaningful_text"

    # Escolher posição inicial aleatória
    start_pos = random.randint(0, len(filtered_words) - num_words)
    return ' '.join(filtered_words[start_pos:start_pos + num_words])


def extract_random_words_from_page(page_text, num_words=10):
    """Extrai num_words palavras consecutivas aleatórias significativas de uma página, evitando referências."""
    # Clean text first to handle hyphenated words
    cleaned_text = clean_text_for_urls(page_text)

    # Check if this page is primarily references - if so, try to extract from non-reference sections
    if is_references_page(cleaned_text):
        # Try to find non-reference sections in this page
        non_ref_text = extract_non_reference_text(cleaned_text)
        if non_ref_text and len(non_ref_text.strip()) > 100:
            cleaned_text = non_ref_text

    # Extrair apenas palavras alfabéticas (não números isolados)
    words = re.findall(r'\b[a-zA-Z][a-zA-Z]+\b', cleaned_text)

    # Filtrar palavras significativas
    filtered_words = filter_meaningful_words(words)

    if len(filtered_words) < num_words:
        if len(filtered_words) >= 3:  # Pelo menos 3 palavras significativas
            return ' '.join(filtered_words)
        else:
            return "insufficient_meaningful_text"

    # Escolher posição inicial aleatória
    start_pos = random.randint(0, len(filtered_words) - num_words)
    return ' '.join(filtered_words[start_pos:start_pos + num_words])


def extract_non_reference_text(page_text):
    """Extrai texto de uma página excluindo seções de referências."""
    # Find references section start
    ref_patterns = [
        r'\bREFERENCES\b',
        r'\bBIBLIOGRAPHY\b',
        r'\bLITERATURE CITED\b',
        r'\bWORKS CITED\b',
        r'\bCITATIONS\b',
        r'\bREFERENCE LIST\b',
        r'\bBIBLIOGRAPHIC REFERENCES\b'
    ]

    ref_start = -1
    for pattern in ref_patterns:
        match = re.search(pattern, page_text, re.IGNORECASE)
        if match:
            if ref_start == -1 or match.start() < ref_start:
                ref_start = match.start()

    if ref_start != -1:
        # Return text before references section
        return page_text[:ref_start]
    else:
        # No references found, return full text
        return page_text


def is_in_references_section(text, target_position):
    """Verifica se a posição do target está na seção de referências."""
    # Procurar por indicadores de seção de referências (mais abrangente)
    ref_patterns = [
        r'\bREFERENCES\b',
        r'\bBIBLIOGRAPHY\b',
        r'\bLITERATURE CITED\b',
        r'\bWORKS CITED\b',
        r'\bCITATIONS\b',
        r'\bREFERENCE LIST\b',
        r'\bBIBLIOGRAPHIC REFERENCES\b'
    ]

    # Encontrar a posição da seção de referências mais próxima antes do target
    ref_start = -1
    for pattern in ref_patterns:
        for match in re.finditer(pattern, text, re.IGNORECASE):
            if match.start() < target_position and match.start() > ref_start:
                ref_start = match.start()

    if ref_start != -1:
        # Verificar se há uma nova seção após as referências e antes do target
        # Padrões que indicam fim da seção de referências
        section_patterns = [
            r'\n\s*[A-Z][A-Z\s]{3,}:?\s*\n',  # Seções em maiúsculas
            r'\n\s*\d+\.?\s+[A-Z][A-Za-z\s]{5,}\n',  # Seções numeradas
            r'\bACKNOWLEDGMENTS?\b',
            r'\bDATA AVAILABILITY\b',
            r'\bSUPPORTING INFORMATION\b',
            r'\bAPPENDI[XC]',
            r'\bFIGURE\s+\d+\b',
            r'\bTABLE\s+\d+\b'
        ]

        # Procurar por nova seção entre referências e target
        text_after_refs = text[ref_start:target_position]
        for pattern in section_patterns:
            if re.search(pattern, text_after_refs, re.IGNORECASE):
                return False  # Há uma nova seção, não está em referências

        return True  # Está na seção de referências

    return False


def is_intro_or_abstract_section(text):
    """Verifica se o texto é de introdução ou abstract (para pular por performance)."""
    # Indicadores de seções a pular
    skip_patterns = [
        r'\bABSTRACT\b',
        r'\bINTRODUCTION\b',
        r'\bBACKGROUND\b',
        r'\bOVERVIEW\b'
    ]

    # Se o texto é muito curto ou contém indicadores de seções a pular
    if len(text.strip()) < 200:  # Textos muito curtos provavelmente não têm dataset info
        return True

    text_upper = text.upper()
    for pattern in skip_patterns:
        if re.search(pattern, text_upper):
            # Se encontrou indicador no início do texto, provavelmente é seção a pular
            if text_upper.find(pattern.replace('\\b', '')) < 500:  # Nos primeiros 500 chars
                return True

    return False


def is_references_page(page_text):
    """Verifica se uma página é principalmente de referências."""
    # Procurar por indicadores de seção de referências
    ref_patterns = [
        r'\bREFERENCES\b',
        r'\bBIBLIOGRAPHY\b',
        r'\bLITERATURE CITED\b',
        r'\bWORKS CITED\b',
        r'\bCITATIONS\b',
        r'\bREFERENCE LIST\b',
        r'\bBIBLIOGRAPHIC REFERENCES\b'
    ]

    text_upper = page_text.upper()

    # Se encontrar indicador de referências nos primeiros 500 caracteres
    for pattern in ref_patterns:
        if re.search(pattern, text_upper):
            match_pos = text_upper.find(pattern.replace('\\b', ''))
            if match_pos != -1 and match_pos < 500:
                return True

    # Verificar se a página tem muitas citações (padrão típico de referências)
    # Contar linhas que começam com números ou autores (padrão de referências)
    lines = page_text.split('\n')
    citation_lines = 0
    total_lines = len([line for line in lines if line.strip()])

    if total_lines > 0:
        for line in lines:
            line = line.strip()
            if line:
                # Padrões típicos de referências
                if (re.match(r'^\d+\.', line) or  # Numeradas
                    re.match(r'^[A-Z][a-z]+,\s*[A-Z]', line) or  # Autor, Inicial
                    re.match(r'^[A-Z][a-z]+\s+[A-Z]\.', line)):  # Autor Inicial.
                    citation_lines += 1

        # Se mais de 60% das linhas parecem citações, é página de referências
        if citation_lines / total_lines > 0.6:
            return True

    return False


def search_in_page_text_no_refs(page_text, variations):
    """Busca as variações do dataset_id em uma página, EXCLUINDO referências."""
    # Primeira tentativa: busca no texto completo, mas só fora das referências
    for variation in variations:
        preceding_words = extract_preceding_words(page_text, variation, num_words=50)
        if preceding_words:
            # Verificar se NÃO está em referências
            target_pos = page_text.lower().find(variation.lower())
            if target_pos != -1 and not is_in_references_section(page_text, target_pos):
                return preceding_words

    # Segunda tentativa: busca por padrões parciais (fora das referências)
    cleaned_text = clean_text_for_urls(page_text)
    for variation in variations:
        if len(variation) > 10:  # Only for longer IDs
            parts = re.split(r'[/\.]', variation)
            for part in parts:
                if len(part) > 5:  # Only meaningful parts
                    if part in cleaned_text:
                        preceding_words = extract_preceding_words(cleaned_text, part, num_words=50)
                        if preceding_words:
                            # Verificar se NÃO está em referências
                            target_pos = cleaned_text.lower().find(part.lower())
                            if target_pos != -1 and not is_in_references_section(cleaned_text, target_pos):
                                return preceding_words

    # Terceira tentativa: busca mais agressiva por qualquer parte do DOI
    for variation in variations:
        # Extrair número DOI se existir
        doi_match = re.search(r'10\.\d+/[\w\d\.-]+', variation)
        if doi_match:
            doi_number = doi_match.group()
            # Buscar por partes menores do DOI
            doi_parts = doi_number.split('/')
            for part in doi_parts:
                if len(part) > 4:  # Partes menores mas significativas
                    if part in page_text:
                        preceding_words = extract_preceding_words(page_text, part, num_words=50)
                        if preceding_words:
                            # Verificar se NÃO está em referências
                            target_pos = page_text.lower().find(part.lower())
                            if target_pos != -1 and not is_in_references_section(page_text, target_pos):
                                return preceding_words

    return None


def search_in_references_only(page_text, variations):
    """Busca as variações do dataset_id APENAS nas seções de referências."""
    # Primeira tentativa: busca direta nas referências
    for variation in variations:
        preceding_words = extract_preceding_words(page_text, variation)
        if preceding_words:
            # Verificar se ESTÁ em referências
            target_pos = page_text.lower().find(variation.lower())
            if target_pos != -1 and is_in_references_section(page_text, target_pos):
                return preceding_words

    # Segunda tentativa: busca por padrões parciais nas referências
    cleaned_text = clean_text_for_urls(page_text)
    for variation in variations:
        if len(variation) > 10:  # Only for longer IDs
            parts = re.split(r'[/\.]', variation)
            for part in parts:
                if len(part) > 5:  # Only meaningful parts
                    if part in cleaned_text:
                        preceding_words = extract_preceding_words(cleaned_text, part)
                        if preceding_words:
                            # Verificar se ESTÁ em referências
                            target_pos = cleaned_text.lower().find(part.lower())
                            if target_pos != -1 and is_in_references_section(cleaned_text, target_pos):
                                return preceding_words

    # Terceira tentativa: busca mais agressiva por qualquer parte do DOI nas referências
    for variation in variations:
        # Extrair número DOI se existir
        doi_match = re.search(r'10\.\d+/[\w\d\.-]+', variation)
        if doi_match:
            doi_number = doi_match.group()
            # Buscar por partes menores do DOI
            doi_parts = doi_number.split('/')
            for part in doi_parts:
                if len(part) > 4:  # Partes menores mas significativas
                    if part in page_text:
                        preceding_words = extract_preceding_words(page_text, part)
                        if preceding_words:
                            # Verificar se ESTÁ em referências
                            target_pos = page_text.lower().find(part.lower())
                            if target_pos != -1 and is_in_references_section(page_text, target_pos):
                                return preceding_words

    return None


def search_in_page_text(page_text, variations):
    """Busca as variações do dataset_id em uma página específica, priorizando fora das referências."""
    # Pular páginas de introdução/abstract por performance
    if is_intro_or_abstract_section(page_text):
        return None

    non_ref_results = []
    ref_results = []

    # Primeira tentativa: busca em seções específicas da página (DATA AVAILABILITY, etc.)
    for variation in variations:
        preceding_words = search_in_sections(page_text, variation)
        if preceding_words:
            # Verificar se está em referências
            target_pos = page_text.lower().find(variation.lower())
            if target_pos != -1 and is_in_references_section(page_text, target_pos):
                ref_results.append(preceding_words)
            else:
                # Prioridade máxima para seções de data availability
                return preceding_words

    # Segunda tentativa: busca no texto completo da página
    for variation in variations:
        preceding_words = extract_preceding_words(page_text, variation)
        if preceding_words:
            # Verificar se está em referências
            target_pos = page_text.lower().find(variation.lower())
            if target_pos != -1 and is_in_references_section(page_text, target_pos):
                ref_results.append(preceding_words)
            else:
                non_ref_results.append(preceding_words)

    # Terceira tentativa: busca por padrões parciais
    cleaned_text = clean_text_for_urls(page_text)
    for variation in variations:
        # Try to find any part of the dataset ID
        if len(variation) > 10:  # Only for longer IDs
            parts = re.split(r'[/\.]', variation)
            for part in parts:
                if len(part) > 5:  # Only meaningful parts
                    if part in cleaned_text:
                        preceding_words = extract_preceding_words(cleaned_text, part, num_words=50)
                        if preceding_words:
                            # Verificar se está em referências
                            target_pos = cleaned_text.lower().find(part.lower())
                            if target_pos != -1 and is_in_references_section(cleaned_text, target_pos):
                                ref_results.append(preceding_words)
                            else:
                                non_ref_results.append(preceding_words)

    # Priorizar resultados fora das referências
    if non_ref_results:
        return non_ref_results[0]  # Retornar o primeiro resultado não-referência
    elif ref_results:
        return ref_results[0]  # Se só tem referências, retornar a primeira

    return None


def process_pdf(article_id, dataset_id, type_value, data_dir="data"):
    """Processa um PDF individual com nova estratégia otimizada."""
    pdf_path = Path(data_dir) / "train" / "PDF" / f"{article_id}.pdf"

    if not pdf_path.exists():
        # Normalizar dataset_id mesmo quando PDF não encontrado
        normalized_dataset_id = normalize_dataset_id_to_standard_format(dataset_id)
        return article_id, normalized_dataset_id, "PDF_NOT_FOUND", type_value

    try:
        # Carregar PDF
        reader = PdfReader(pdf_path)

        if dataset_id == "Missing":
            # Para casos Missing, tentar encontrar uma página que não seja de referências
            if len(reader.pages) > 0:
                # Try to find a non-reference page first
                non_ref_pages = []
                for page_idx in range(len(reader.pages)):
                    try:
                        page_text = reader.pages[page_idx].extract_text()
                        if page_text.strip() and not is_references_page(page_text) and not is_intro_or_abstract_section(page_text):
                            non_ref_pages.append(page_idx)
                    except Exception:
                        continue

                # If we found non-reference pages, use one of them
                if non_ref_pages:
                    random_page_idx = random.choice(non_ref_pages)
                else:
                    # Fallback to any random page
                    random_page_idx = random.randint(0, len(reader.pages) - 1)

                try:
                    page_text = reader.pages[random_page_idx].extract_text()
                    random_words = extract_random_words_from_page(page_text, num_words=50)
                    return article_id, "Missing", random_words, type_value
                except Exception:
                    return article_id, "Missing", "ERROR_EXTRACTING_RANDOM_PAGE", type_value
            else:
                return article_id, "Missing", "PDF_NO_PAGES", type_value

        # Nova estratégia para dataset_ids não-Missing
        # Normalizar dataset_id para formato padrão
        normalized_dataset_id = normalize_dataset_id_to_standard_format(dataset_id)

        # Usar dataset_id original para busca (mais variações)
        variations = normalize_dataset_id(dataset_id)
        total_pages = len(reader.pages)

        if total_pages == 0:
            return article_id, normalized_dataset_id, "PDF_NO_PAGES", type_value

        # FASE 1: Começar pela metade do PDF e ir até o final (excluindo referências)
        middle_page = total_pages // 2

        for page_idx in range(middle_page, total_pages):
            try:
                page_text = reader.pages[page_idx].extract_text()
                if not page_text.strip():
                    continue

                # Verificar se chegamos nas referências - se sim, parar esta fase
                if is_references_page(page_text):
                    break

                # Buscar dataset_id nesta página (excluindo referências)
                result = search_in_page_text_no_refs(page_text, variations)
                if result:
                    return article_id, normalized_dataset_id, result, type_value

            except Exception:
                continue

        # FASE 2: Se não encontrou, começar do início até a metade (ignorando abstract/introduction)
        for page_idx in range(0, middle_page):
            try:
                page_text = reader.pages[page_idx].extract_text()
                if not page_text.strip():
                    continue

                # Pular páginas de abstract/introduction
                if is_intro_or_abstract_section(page_text):
                    continue

                # Buscar dataset_id nesta página (excluindo referências)
                result = search_in_page_text_no_refs(page_text, variations)
                if result:
                    return article_id, normalized_dataset_id, result, type_value

            except Exception:
                continue

        # FASE 3: Como último recurso, buscar nas referências
        ref_results = []
        for page_idx in range(total_pages):
            try:
                page_text = reader.pages[page_idx].extract_text()
                if not page_text.strip():
                    continue

                # Buscar apenas nas referências
                result = search_in_references_only(page_text, variations)
                if result:
                    ref_results.append(result)

            except Exception:
                continue

        # Retornar primeiro resultado das referências se encontrou
        if ref_results:
            return article_id, normalized_dataset_id, ref_results[0], type_value

        # Se não encontrou em lugar nenhum
        return article_id, normalized_dataset_id, "DATASET_ID_NOT_FOUND", type_value

    except Exception as e:
        # Normalizar dataset_id mesmo em caso de erro
        normalized_dataset_id = normalize_dataset_id_to_standard_format(dataset_id)
        return article_id, normalized_dataset_id, f"ERROR: {str(e)}", type_value


def fix_existing_csv():
    """Fix incomplete dataset IDs in existing partial_extraction.csv file."""
    csv_path = Path("results") / "partial_extraction.csv"

    if not csv_path.exists():
        print(f"❌ Arquivo {csv_path} não encontrado.")
        return

    print(f"🔧 Corrigindo dataset IDs incompletos em {csv_path}...")

    # Read the CSV file
    df = pd.read_csv(csv_path)
    original_count = len(df)

    # Count incomplete IDs before conversion
    incomplete_before = 0
    for dataset_id in df['dataset_id']:
        if str(dataset_id) not in ['Missing', 'DATASET_ID_NOT_FOUND'] and not str(dataset_id).startswith('http'):
            incomplete_before += 1

    # Apply conversion to dataset_id column
    df['dataset_id'] = df['dataset_id'].apply(convert_incomplete_dataset_id_to_doi)

    # Count incomplete IDs after conversion
    incomplete_after = 0
    for dataset_id in df['dataset_id']:
        if str(dataset_id) not in ['Missing', 'DATASET_ID_NOT_FOUND'] and not str(dataset_id).startswith('http'):
            incomplete_after += 1

    # Save the updated CSV
    output_path = csv_path.parent / "partial_extraction_fixed.csv"
    df.to_csv(output_path, index=False)

    conversions = incomplete_before - incomplete_after

    print(f"✅ Dataset IDs corrigidos salvos em: {output_path}")
    print(f"📊 Estatísticas da correção:")
    print(f"   Total de entradas: {original_count}")
    print(f"   IDs incompletos antes: {incomplete_before}")
    print(f"   IDs incompletos depois: {incomplete_after}")
    print(f"   IDs convertidos para DOI: {conversions}")

    return output_path


def main():
    """Função principal para processar TODOS os PDFs."""
    print("=== Extração Completa de Dataset IDs (Nova Estratégia Otimizada) ===")
    print("Este script processará TODOS os PDFs com nova estratégia:")
    print("1. Começar pela metade do PDF até o final (excluindo referências)")
    print("2. Se não encontrar, buscar do início até a metade (ignorando abstract/intro)")
    print("3. Como último recurso, buscar nas referências")
    print("4. Normalizar todos os dataset_ids para formato https://doi.org.br/doi_number")
    print("5. Converter IDs incompletos (CHEMBL, IPR, etc.) para formato DOI completo")
    print("Estimativa de tempo: 5-15 minutos.\n")

    # Check if user wants to fix existing CSV first
    csv_path = Path("results") / "partial_extraction.csv"
    if csv_path.exists():
        print(f"📁 Encontrado arquivo existente: {csv_path}")
        response = input("Deseja corrigir os dataset IDs incompletos no arquivo existente? (s/n): ").strip().lower()
        if response in ['s', 'sim', 'y', 'yes']:
            fix_existing_csv()
            print("\n" + "="*60 + "\n")

    print("Agora você pode escolher:")
    print("1. Processar todos os PDFs novamente (recomendado se houver mudanças)")
    print("2. Sair (se apenas queria corrigir o arquivo existente)")

    response = input("Escolha uma opção (1/2): ").strip()
    if response == "2":
        print("Saindo...")
        return
    
    # Carregar dados
    labels_df = pd.read_csv("data/train_labels.csv")
    total_files = len(labels_df)

    print(f"Total de entradas a processar: {total_files}")
    
    # Confirmar se o usuário quer continuar
    response = input("Deseja continuar? (s/n): ").strip().lower()
    if response not in ['s', 'sim', 'y', 'yes']:
        print("Processamento cancelado.")
        return
    
    print(f"\nIniciando processamento de {total_files} entradas...")
    
    results = []
    
    # Usar tqdm para barra de progresso
    for idx, row in tqdm(labels_df.iterrows(), total=total_files, desc="Processando PDFs"):
        article_id = row['article_id']
        dataset_id = row['dataset_id']
        type_value = row['type']

        result = process_pdf(article_id, dataset_id, type_value)
        results.append(result)
        
        # Salvar resultados parciais a cada 50 arquivos (mais frequente devido à velocidade)
        if (idx + 1) % 50 == 0:
            partial_df = pd.DataFrame(results, columns=[
                'article_id', 'dataset_id', 'preceding_words', 'type'
            ])
            
            results_dir = Path("results")
            results_dir.mkdir(exist_ok=True)
            
            partial_path = results_dir / f"partial_extraction.csv"
            partial_df.to_csv(partial_path, index=False, encoding='utf-8')
            print(f"\nSalvamento parcial: {partial_path}")
    
    # Criar DataFrame final com resultados
    results_df = pd.DataFrame(results, columns=[
        'article_id', 'dataset_id', 'preceding_words', 'type'
    ])
    
    # Salvar resultados finais
    results_dir = Path("results")
    results_dir.mkdir(exist_ok=True)
    
    output_path = results_dir / "complete_pdf_extraction_results.csv"
    results_df.to_csv(output_path, index=False, encoding='utf-8')
    
    print(f"\n✅ Resultados finais salvos em: {output_path}")
    
    # Mostrar estatísticas finais
    total = len(results_df)
    found = len(results_df[~results_df['preceding_words'].str.contains('NOT_FOUND|ERROR', na=False)])
    missing = len(results_df[results_df['dataset_id'] == 'Missing'])
    not_found = total - found
    
    print(f"\n📊 Estatísticas Finais:")
    print(f"Total de entradas: {total}")
    print(f"Dataset IDs encontrados: {found - missing} ({((found - missing) / (total - missing) * 100):.1f}% dos não-Missing)")
    print(f"Casos Missing: {missing}")
    print(f"Não encontrados/Erros: {not_found}")
    
    # Mostrar alguns exemplos de sucessos
    success_cases = results_df[
        (~results_df['preceding_words'].str.contains('NOT_FOUND|ERROR', na=False)) &
        (results_df['dataset_id'] != 'Missing')
    ]
    
    if len(success_cases) > 0:
        print(f"\n🎯 Exemplos de Dataset IDs encontrados:")
        for idx, row in success_cases.head(5).iterrows():
            print(f"  📄 {row['article_id']}")
            print(f"  🔗 {row['dataset_id']}")
            print(f"  📝 {row['preceding_words']}")
            print()
    
    print("🎉 Processamento completo finalizado!")


if __name__ == "__main__":
    try:
        main()
    except KeyboardInterrupt:
        print("\n⏹️  Processamento interrompido pelo usuário.")
        print("Os resultados parciais foram salvos na pasta 'results/'.")
    except Exception as e:
        print(f"\n❌ Erro durante o processamento: {e}")
        import traceback
        traceback.print_exc()
